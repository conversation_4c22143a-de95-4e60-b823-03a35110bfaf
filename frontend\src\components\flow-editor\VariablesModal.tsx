import { RpaFlow, FlowExecution } from '@rpa-project/shared'
import { Modal } from '../ui/Modal'

interface VariablesModalProps {
  isOpen: boolean
  onClose: () => void
  flow: RpaFlow
  currentExecution: FlowExecution | null
}

export function VariablesModal({ isOpen, onClose, flow, currentExecution }: VariablesModalProps) {
  const getVariablesFromLogs = (execution: FlowExecution | null): Record<string, any> => {
    if (!execution || !execution.logs) return {}
    
    const variables: Record<string, any> = {}
    
    // Extract variables from log data
    execution.logs.forEach(log => {
      if (log.data && typeof log.data === 'object') {
        Object.assign(variables, log.data)
      }
    })
    
    return variables
  }

  const getExpectedVariables = (): Record<string, string> => {
    if (!flow) return {}
    
    const expectedVars: Record<string, string> = {}
    
    // Find steps that create variables
    flow.steps.forEach(step => {
      if (step.type === 'extractText' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Text från: ${(step as any).selector}`
      }
      if (step.type === 'extractAttribute' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Attribut ${(step as any).attribute} från: ${(step as any).selector}`
      }
      if (step.type === 'takeScreenshot' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Base64 skärmdump${(step as any).path ? ` från: ${(step as any).path}` : ''}`
      }
      if (step.type === 'downloadFile' && (step as any).variableName) {
        expectedVars[(step as any).variableName] = `Base64 filinnehåll${(step as any).filename ? ` från: ${(step as any).filename}` : ''}`
      }
    })
    
    return expectedVars
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="🔧 Flödesvariabler"
      size="xl"
    >
      <div style={{ padding: '1.5rem' }}>
            {/* Expected Variables Section */}
            <div style={{ marginBottom: '2rem' }}>
              <h2 className="section-title" style={{ padding: 0, marginBottom: '0.75rem' }}>
                Förväntade variabler (från flödessteg)
              </h2>
              <div className="info-box">
                {(() => {
                  const expectedVars = getExpectedVariables()
                  return Object.keys(expectedVars).length > 0 ? (
                    <div style={{ display: 'grid', gap: '0.75rem' }}>
                      {Object.entries(expectedVars).map(([key, description]) => (
                        <div key={key} className="table-container" style={{
                          padding: 0,
                          marginBottom: 0
                        }}>
                          <div className="activity-table">
                            <table className="table">
                              <tbody>
                                <tr>
                                  <td style={{
                                    fontWeight: '500',
                                    color: '#1a0f0f',
                                    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                                    width: '30%'
                                  }}>
                                    ${key}
                                  </td>
                                  <td className="secondary-text">
                                    {description}
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="empty-state">
                      <div className="empty-state-icon">📝</div>
                      <p className="empty-state-title">Inga variabler definierade</p>
                      <p className="empty-state-subtitle">Inga steg som skapar variabler hittades i detta flöde</p>
                    </div>
                  )
                })()}
              </div>
            </div>

            {/* Runtime Variables Section */}
            <div>
              <h2 className="section-title" style={{ padding: 0, marginBottom: '0.75rem' }}>
                Runtime-variabler (från senaste körning)
              </h2>
              <div className="table-container" style={{ padding: 0 }}>
                <div className="activity-table">
                  {(() => {
                    const runtimeVariables = {
                      ...(currentExecution?.results || {}),
                      ...getVariablesFromLogs(currentExecution)
                    }
                    return Object.keys(runtimeVariables).length > 0 ? (
                      <table className="table">
                        <thead>
                          <tr>
                            <th style={{ width: '30%' }}>Variabelnamn</th>
                            <th>Värde</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(runtimeVariables).map(([key, value]) => (
                            <tr key={key}>
                              <td style={{
                                fontWeight: '500',
                                color: '#1a0f0f',
                                fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                              }}>
                                ${key}
                              </td>
                              <td>
                                <div style={{
                                  color: '#6b7280',
                                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                                  fontSize: '0.875rem',
                                  wordBreak: 'break-all',
                                  maxWidth: '400px',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis'
                                }}>
                                  {(() => {
                                    const stringValue = typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value);

                                    // Check if this looks like base64 data (long string with base64 characters)
                                    if (typeof value === 'string' && value.length > 100 && /^[A-Za-z0-9+/=]+$/.test(value)) {
                                      return (
                                        <details style={{ cursor: 'pointer' }}>
                                          <summary style={{
                                            color: '#fd746c',
                                            fontWeight: '500',
                                            userSelect: 'none'
                                          }}>
                                            📷 Base64 bild ({value.length} tecken) - klicka för att visa
                                          </summary>
                                          <div style={{
                                            marginTop: '0.5rem',
                                            padding: '0.5rem',
                                            backgroundColor: '#f9fafb',
                                            borderRadius: '0.25rem',
                                            maxHeight: '200px',
                                            overflow: 'auto'
                                          }}>
                                            <div style={{ marginBottom: '0.5rem' }}>
                                              <img
                                                src={`data:image/png;base64,${value}`}
                                                alt="Screenshot"
                                                style={{
                                                  maxWidth: '200px',
                                                  maxHeight: '150px',
                                                  border: '1px solid #e5e7eb',
                                                  borderRadius: '0.25rem'
                                                }}
                                              />
                                            </div>
                                            <div style={{
                                              fontSize: '0.75rem',
                                              color: '#9ca3af',
                                              wordBreak: 'break-all',
                                              maxHeight: '100px',
                                              overflow: 'auto'
                                            }}>
                                              {stringValue.substring(0, 200)}...
                                            </div>
                                          </div>
                                        </details>
                                      );
                                    }

                                    return stringValue;
                                  })()}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    ) : (
                      <div className="empty-state">
                        <div className="empty-state-icon">🔍</div>
                        <p className="empty-state-title">Inga runtime-variabler</p>
                        <p className="empty-state-subtitle">
                          {currentExecution ? 'Inga runtime-variabler från senaste körning' : 'Ingen körning tillgänglig - kör flödet för att se runtime-variabler'}
                        </p>
                      </div>
                    )
                  })()}
                </div>
              </div>
            </div>
      </div>
    </Modal>
  )
}
